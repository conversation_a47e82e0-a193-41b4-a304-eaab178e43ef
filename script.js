document.addEventListener('DOMContentLoaded', () => {
    const connectBtn = document.getElementById('connect-btn');
    const dbStringInput = document.getElementById('db-string');
    const loginContainer = document.getElementById('login-container');
    const mainContainer = document.getElementById('main-container');
    const tableList = document.getElementById('table-list');
    const tableName = document.getElementById('table-name');
    const tableData = document.getElementById('table-data');

    connectBtn.addEventListener('click', async () => {
        const connectionString = dbStringInput.value;
        if (!connectionString) {
            alert('Please enter a connection string.');
            return;
        }

        try {
            const response = await fetch('http://localhost:3000/connect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ connectionString }),
            });

            if (response.ok) {
                loginContainer.style.display = 'none';
                mainContainer.style.display = 'flex';
                loadTables();
            } else {
                alert('Failed to connect to the database.');
            }
        } catch (error) {
            console.error('Error connecting to database:', error);
            alert('An error occurred while connecting to the database.');
        }
    });

    async function loadTables() {
        try {
            const response = await fetch('http://localhost:3000/tables');
            const tables = await response.json();
            tableList.innerHTML = '';
            tables.forEach(table => {
                const li = document.createElement('li');
                li.textContent = table;
                li.addEventListener('click', () => loadTableData(table));
                tableList.appendChild(li);
            });
        } catch (error) {
            console.error('Error loading tables:', error);
        }
    }

    async function loadTableData(table) {
        try {
            tableName.textContent = table;
            const response = await fetch(`http://localhost:3000/tables/${table}`);
            const data = await response.json();
            renderTable(data, table);
        } catch (error) {
            console.error(`Error loading data for table ${table}:`, error);
        }
    }

    function renderTable(data, tableName) {
        if (!data.length) {
            tableData.innerHTML = '<p>No data in this table.</p>';
            return;
        }

        const table = document.createElement('table');
        const thead = document.createElement('thead');
        const tbody = document.createElement('tbody');
        const headers = Object.keys(data[0]);

        const headerRow = document.createElement('tr');
        headers.forEach(header => {
            const th = document.createElement('th');
            th.textContent = header;
            headerRow.appendChild(th);
        });
        const actionsTh = document.createElement('th');
        actionsTh.textContent = 'Actions';
        headerRow.appendChild(actionsTh);
        thead.appendChild(headerRow);

        data.forEach(row => {
            const tr = document.createElement('tr');
            headers.forEach(header => {
                const td = document.createElement('td');
                td.textContent = row[header];
                td.setAttribute('contenteditable', 'true');
                td.addEventListener('blur', (e) => {
                    updateCell(tableName, row.id, header, e.target.textContent);
                });
                tr.appendChild(td);
            });

            const actionsTd = document.createElement('td');
            const deleteBtn = document.createElement('button');
            deleteBtn.textContent = 'Delete';
            deleteBtn.addEventListener('click', () => deleteRow(tableName, row.id));
            actionsTd.appendChild(deleteBtn);
            tr.appendChild(actionsTd);

            tbody.appendChild(tr);
        });

        table.appendChild(thead);
        table.appendChild(tbody);
        tableData.innerHTML = '';
        tableData.appendChild(table);
    }

    async function updateCell(tableName, id, column, value) {
        try {
            await fetch(`http://localhost:3000/tables/${tableName}/${id}`,
             {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ [column]: value }),
            });
        } catch (error) {
            console.error('Error updating cell:', error);
        }
    }

    async function deleteRow(tableName, id) {
        try {
            await fetch(`http://localhost:3000/tables/${tableName}/${id}`, {
                method: 'DELETE',
            });
            loadTableData(tableName);
        } catch (error) {
            console.error('Error deleting row:', error);
        }
    }
});
