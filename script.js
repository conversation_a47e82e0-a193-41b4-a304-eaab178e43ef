// Database Manager Application
class DatabaseManager {
    constructor() {
        this.connectionId = null;
        this.currentTable = null;
        this.currentSchema = null;
        this.currentPage = 1;
        this.pageSize = 50;
        this.searchTerm = '';
        this.tableStructure = [];
        this.primaryKey = null;

        this.init();
    }

    init() {
        this.bindEvents();
        this.showConnectionForm();
    }

    bindEvents() {
        // Connection form
        document.getElementById('connection-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleConnection();
        });

        // Disconnect button
        document.getElementById('disconnect-btn').addEventListener('click', () => {
            this.disconnect();
        });

        // Refresh tables
        document.getElementById('refresh-tables').addEventListener('click', () => {
            this.loadTables();
        });

        // Table search
        document.getElementById('table-search').addEventListener('input', (e) => {
            this.filterTables(e.target.value);
        });

        // Data search
        document.getElementById('data-search').addEventListener('input', (e) => {
            this.searchTerm = e.target.value;
            this.currentPage = 1;
            this.loadTableData();
        });

        // Add record button
        document.getElementById('add-record-btn').addEventListener('click', () => {
            this.showAddRecordModal();
        });

        // Refresh data button
        document.getElementById('refresh-data-btn').addEventListener('click', () => {
            this.loadTableData();
        });

        // Pagination
        document.getElementById('prev-page').addEventListener('click', () => {
            if (this.currentPage > 1) {
                this.currentPage--;
                this.loadTableData();
            }
        });

        document.getElementById('next-page').addEventListener('click', () => {
            this.currentPage++;
            this.loadTableData();
        });

        // Modal events
        document.getElementById('close-modal').addEventListener('click', () => {
            this.hideModal('record-modal');
        });

        document.getElementById('cancel-btn').addEventListener('click', () => {
            this.hideModal('record-modal');
        });

        document.getElementById('record-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleRecordSubmit();
        });

        // Confirmation modal events
        document.getElementById('close-confirm-modal').addEventListener('click', () => {
            this.hideModal('confirm-modal');
        });

        document.getElementById('confirm-cancel').addEventListener('click', () => {
            this.hideModal('confirm-modal');
        });

        // Click outside modal to close
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.hideModal(e.target.id);
            }
        });
    }

    async handleConnection() {
        const form = document.getElementById('connection-form');
        const formData = new FormData(form);
        const connectionString = formData.get('connectionString');
        const connectBtn = document.getElementById('connect-btn');
        const statusDiv = document.getElementById('connection-status');

        if (!connectionString.trim()) {
            this.showStatus('Please enter a connection string', 'error');
            return;
        }

        connectBtn.disabled = true;
        connectBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Connecting...';
        statusDiv.innerHTML = '';

        try {
            const response = await fetch('http://localhost:3000/api/test-connection', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ connectionString }),
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.connectionId = result.connectionId;
                this.showStatus('Connection successful!', 'success');
                setTimeout(() => {
                    this.showMainApplication();
                }, 1000);
            } else {
                this.showStatus(result.error || 'Connection failed', 'error');
            }
        } catch (error) {
            console.error('Connection error:', error);
            this.showStatus('Failed to connect to server', 'error');
        } finally {
            connectBtn.disabled = false;
            connectBtn.innerHTML = '<i class="fas fa-plug"></i> Connect to Database';
        }
    }

    showStatus(message, type) {
        const statusDiv = document.getElementById('connection-status');
        statusDiv.innerHTML = message;
        statusDiv.className = `status-message ${type}`;
    }

    showConnectionForm() {
        document.getElementById('connection-container').style.display = 'flex';
        document.getElementById('main-container').style.display = 'none';
    }

    showMainApplication() {
        document.getElementById('connection-container').style.display = 'none';
        document.getElementById('main-container').style.display = 'flex';
        this.loadTables();
    }

    async loadTables() {
        const tablesList = document.getElementById('tables-list');
        tablesList.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Loading tables...</div>';

        try {
            const response = await fetch(`http://localhost:3000/api/tables/${this.connectionId}`);
            const tables = await response.json();

            if (response.ok) {
                this.renderTables(tables);
            } else {
                tablesList.innerHTML = '<div class="error">Failed to load tables</div>';
                this.showToast('Failed to load tables', 'error');
            }
        } catch (error) {
            console.error('Error loading tables:', error);
            tablesList.innerHTML = '<div class="error">Failed to load tables</div>';
            this.showToast('Failed to load tables', 'error');
        }
    }

    renderTables(tables) {
        const tablesList = document.getElementById('tables-list');

        if (tables.length === 0) {
            tablesList.innerHTML = '<div class="no-data"><i class="fas fa-inbox"></i><p>No tables found</p></div>';
            return;
        }

        const tablesHtml = tables.map(table => `
            <div class="table-item" data-schema="${table.table_schema}" data-table="${table.table_name}">
                <i class="fas fa-table"></i>
                <div class="table-info">
                    <div class="table-name">${table.table_name}</div>
                    <div class="table-schema">${table.table_schema}</div>
                </div>
            </div>
        `).join('');

        tablesList.innerHTML = tablesHtml;

        // Add click events to table items
        tablesList.querySelectorAll('.table-item').forEach(item => {
            item.addEventListener('click', () => {
                const schema = item.dataset.schema;
                const tableName = item.dataset.table;
                this.selectTable(schema, tableName, item);
            });
        });
    }

    filterTables(searchTerm) {
        const tableItems = document.querySelectorAll('.table-item');
        const term = searchTerm.toLowerCase();

        tableItems.forEach(item => {
            const tableName = item.querySelector('.table-name').textContent.toLowerCase();
            const schema = item.querySelector('.table-schema').textContent.toLowerCase();

            if (tableName.includes(term) || schema.includes(term)) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        });
    }

    async selectTable(schema, tableName, element) {
        // Update active state
        document.querySelectorAll('.table-item').forEach(item => {
            item.classList.remove('active');
        });
        element.classList.add('active');

        this.currentSchema = schema;
        this.currentTable = tableName;
        this.currentPage = 1;

        // Show table view
        document.getElementById('welcome-screen').style.display = 'none';
        document.getElementById('table-view').style.display = 'flex';
        document.getElementById('current-table-name').textContent = `${schema}.${tableName}`;

        // Load table structure and data
        await this.loadTableStructure();
        await this.loadPrimaryKey();
        await this.loadTableData();
    }

    async loadTableStructure() {
        try {
            const response = await fetch(
                `http://localhost:3000/api/table-structure/${this.connectionId}/${this.currentSchema}/${this.currentTable}`
            );
            const structure = await response.json();

            if (response.ok) {
                this.tableStructure = structure;
            } else {
                this.showToast('Failed to load table structure', 'error');
            }
        } catch (error) {
            console.error('Error loading table structure:', error);
            this.showToast('Failed to load table structure', 'error');
        }
    }

    async loadPrimaryKey() {
        try {
            const response = await fetch(
                `http://localhost:3000/api/primary-key/${this.connectionId}/${this.currentSchema}/${this.currentTable}`
            );
            const result = await response.json();

            if (response.ok) {
                this.primaryKey = result.primaryKey;
            }
        } catch (error) {
            console.error('Error loading primary key:', error);
        }
    }

    async loadTableData() {
        const tableLoading = document.getElementById('table-loading');
        const dataTable = document.getElementById('data-table');
        const noData = document.getElementById('no-data');

        tableLoading.style.display = 'flex';
        dataTable.style.display = 'none';
        noData.style.display = 'none';

        try {
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                search: this.searchTerm
            });

            const response = await fetch(
                `http://localhost:3000/api/table-data/${this.connectionId}/${this.currentSchema}/${this.currentTable}?${params}`
            );
            const result = await response.json();

            if (response.ok) {
                this.renderTableData(result.data, result.pagination);
            } else {
                this.showToast('Failed to load table data', 'error');
                noData.style.display = 'flex';
            }
        } catch (error) {
            console.error('Error loading table data:', error);
            this.showToast('Failed to load table data', 'error');
            noData.style.display = 'flex';
        } finally {
            tableLoading.style.display = 'none';
        }
    }

    renderTableData(data, pagination) {
        const tableHead = document.getElementById('table-head');
        const tableBody = document.getElementById('table-body');
        const dataTable = document.getElementById('data-table');
        const noData = document.getElementById('no-data');

        if (data.length === 0) {
            dataTable.style.display = 'none';
            noData.style.display = 'flex';
            this.updatePagination(pagination);
            return;
        }

        // Render table headers
        const columns = Object.keys(data[0]);
        const headersHtml = `
            <tr>
                ${columns.map(col => `<th>${col}</th>`).join('')}
                <th class="actions-cell">Actions</th>
            </tr>
        `;
        tableHead.innerHTML = headersHtml;

        // Render table rows
        const rowsHtml = data.map(row => {
            const rowId = row[this.primaryKey] || row[columns[0]];
            return `
                <tr>
                    ${columns.map(col => `<td title="${this.escapeHtml(String(row[col] || ''))}">${this.escapeHtml(String(row[col] || ''))}</td>`).join('')}
                    <td class="actions-cell">
                        <div class="record-actions">
                            <button class="btn-icon edit-btn" data-id="${rowId}" title="Edit">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn-icon delete-btn" data-id="${rowId}" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');
        tableBody.innerHTML = rowsHtml;

        // Add event listeners to action buttons
        tableBody.querySelectorAll('.edit-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const rowId = e.target.closest('.edit-btn').dataset.id;
                const rowData = data.find(row => String(row[this.primaryKey] || row[columns[0]]) === rowId);
                this.showEditRecordModal(rowData);
            });
        });

        tableBody.querySelectorAll('.delete-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const rowId = e.target.closest('.delete-btn').dataset.id;
                this.showDeleteConfirmation(rowId);
            });
        });

        dataTable.style.display = 'table';
        noData.style.display = 'none';
        this.updatePagination(pagination);
        this.updateRecordCount(pagination.total);
    }

    updatePagination(pagination) {
        const { page, totalPages, total } = pagination;

        // Update pagination info
        document.getElementById('pagination-text').textContent = `Page ${page} of ${totalPages}`;
        document.getElementById('records-info').textContent =
            `Showing ${((page - 1) * this.pageSize) + 1} to ${Math.min(page * this.pageSize, total)} of ${total} records`;

        // Update pagination buttons
        document.getElementById('prev-page').disabled = page <= 1;
        document.getElementById('next-page').disabled = page >= totalPages;

        // Update page numbers
        this.renderPageNumbers(page, totalPages);
    }

    renderPageNumbers(currentPage, totalPages) {
        const pageNumbers = document.getElementById('page-numbers');
        const maxVisible = 5;
        let startPage = Math.max(1, currentPage - Math.floor(maxVisible / 2));
        let endPage = Math.min(totalPages, startPage + maxVisible - 1);

        if (endPage - startPage + 1 < maxVisible) {
            startPage = Math.max(1, endPage - maxVisible + 1);
        }

        let html = '';
        for (let i = startPage; i <= endPage; i++) {
            html += `
                <button class="page-number ${i === currentPage ? 'active' : ''}" data-page="${i}">
                    ${i}
                </button>
            `;
        }

        pageNumbers.innerHTML = html;

        // Add click events
        pageNumbers.querySelectorAll('.page-number').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.currentPage = parseInt(e.target.dataset.page);
                this.loadTableData();
            });
        });
    }

    updateRecordCount(total) {
        document.getElementById('table-record-count').textContent = `${total} records`;
    }

    showAddRecordModal() {
        document.getElementById('modal-title').textContent = 'Add New Record';
        document.getElementById('save-btn').innerHTML = '<i class="fas fa-save"></i> Save Record';
        this.renderRecordForm();
        this.showModal('record-modal');
    }

    showEditRecordModal(rowData) {
        document.getElementById('modal-title').textContent = 'Edit Record';
        document.getElementById('save-btn').innerHTML = '<i class="fas fa-save"></i> Update Record';
        this.renderRecordForm(rowData);
        this.showModal('record-modal');
    }

    renderRecordForm(data = {}) {
        const formFields = document.getElementById('form-fields');

        const fieldsHtml = this.tableStructure.map(column => {
            const value = data[column.column_name] || '';
            const isRequired = column.is_nullable === 'NO' && !column.column_default;

            return `
                <div class="form-field">
                    <label for="field-${column.column_name}">
                        ${column.column_name}
                        ${isRequired ? '*' : ''}
                        <small>(${column.data_type})</small>
                    </label>
                    ${this.renderFormInput(column, value)}
                </div>
            `;
        }).join('');

        formFields.innerHTML = fieldsHtml;
    }

    renderFormInput(column, value) {
        const { column_name, data_type, is_nullable } = column;
        const required = is_nullable === 'NO' ? 'required' : '';

        if (data_type.includes('text') || data_type.includes('varchar') || data_type.includes('char')) {
            if (column.character_maximum_length > 255) {
                return `<textarea id="field-${column_name}" name="${column_name}" ${required}>${this.escapeHtml(value)}</textarea>`;
            } else {
                return `<input type="text" id="field-${column_name}" name="${column_name}" value="${this.escapeHtml(value)}" ${required}>`;
            }
        } else if (data_type.includes('int') || data_type.includes('numeric') || data_type.includes('decimal')) {
            return `<input type="number" id="field-${column_name}" name="${column_name}" value="${value}" ${required}>`;
        } else if (data_type.includes('bool')) {
            return `
                <select id="field-${column_name}" name="${column_name}" ${required}>
                    <option value="">Select...</option>
                    <option value="true" ${value === true || value === 'true' ? 'selected' : ''}>True</option>
                    <option value="false" ${value === false || value === 'false' ? 'selected' : ''}>False</option>
                </select>
            `;
        } else if (data_type.includes('date') || data_type.includes('time')) {
            const inputType = data_type.includes('timestamp') ? 'datetime-local' :
                             data_type.includes('date') ? 'date' : 'time';
            let formattedValue = value;
            if (inputType === 'datetime-local' && value) {
                formattedValue = new Date(value).toISOString().slice(0, 16);
            }
            return `<input type="${inputType}" id="field-${column_name}" name="${column_name}" value="${formattedValue}" ${required}>`;
        } else {
            return `<input type="text" id="field-${column_name}" name="${column_name}" value="${this.escapeHtml(value)}" ${required}>`;
        }
    }

    async handleRecordSubmit() {
        const form = document.getElementById('record-form');
        const formData = new FormData(form);
        const data = {};

        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }

        const saveBtn = document.getElementById('save-btn');
        const originalText = saveBtn.innerHTML;
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';

        try {
            const isEdit = document.getElementById('modal-title').textContent === 'Edit Record';
            let response;

            if (isEdit) {
                const recordId = this.getRecordIdFromForm(data);
                response = await fetch(
                    `http://localhost:3000/api/table-data/${this.connectionId}/${this.currentSchema}/${this.currentTable}/${recordId}`,
                    {
                        method: 'PUT',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ data, primaryKey: this.primaryKey })
                    }
                );
            } else {
                response = await fetch(
                    `http://localhost:3000/api/table-data/${this.connectionId}/${this.currentSchema}/${this.currentTable}`,
                    {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ data })
                    }
                );
            }

            const result = await response.json();

            if (response.ok) {
                this.showToast(isEdit ? 'Record updated successfully' : 'Record added successfully', 'success');
                this.hideModal('record-modal');
                this.loadTableData();
            } else {
                this.showToast(result.error || 'Failed to save record', 'error');
            }
        } catch (error) {
            console.error('Error saving record:', error);
            this.showToast('Failed to save record', 'error');
        } finally {
            saveBtn.disabled = false;
            saveBtn.innerHTML = originalText;
        }
    }

    getRecordIdFromForm(data) {
        return data[this.primaryKey] || Object.values(data)[0];
    }

    showDeleteConfirmation(recordId) {
        document.getElementById('confirm-message').textContent =
            'Are you sure you want to delete this record? This action cannot be undone.';

        const confirmBtn = document.getElementById('confirm-action');
        confirmBtn.onclick = () => this.deleteRecord(recordId);

        this.showModal('confirm-modal');
    }

    async deleteRecord(recordId) {
        this.hideModal('confirm-modal');

        try {
            const params = new URLSearchParams({ primaryKey: this.primaryKey });
            const response = await fetch(
                `http://localhost:3000/api/table-data/${this.connectionId}/${this.currentSchema}/${this.currentTable}/${recordId}?${params}`,
                { method: 'DELETE' }
            );

            const result = await response.json();

            if (response.ok) {
                this.showToast('Record deleted successfully', 'success');
                this.loadTableData();
            } else {
                this.showToast(result.error || 'Failed to delete record', 'error');
            }
        } catch (error) {
            console.error('Error deleting record:', error);
            this.showToast('Failed to delete record', 'error');
        }
    }

    showModal(modalId) {
        document.getElementById(modalId).style.display = 'flex';
        document.body.style.overflow = 'hidden';
    }

    hideModal(modalId) {
        document.getElementById(modalId).style.display = 'none';
        document.body.style.overflow = 'auto';
    }

    showToast(message, type = 'info', duration = 5000) {
        const toastContainer = document.getElementById('toast-container');
        const toastId = 'toast-' + Date.now();

        const iconMap = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        const toast = document.createElement('div');
        toast.id = toastId;
        toast.className = `toast ${type}`;
        toast.innerHTML = `
            <i class="${iconMap[type]}"></i>
            <div class="toast-content">
                <div class="toast-title">${type.charAt(0).toUpperCase() + type.slice(1)}</div>
                <div class="toast-message">${message}</div>
            </div>
            <button class="toast-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;

        toastContainer.appendChild(toast);

        // Auto remove after duration
        setTimeout(() => {
            const toastElement = document.getElementById(toastId);
            if (toastElement) {
                toastElement.remove();
            }
        }, duration);
    }

    async disconnect() {
        if (this.connectionId) {
            try {
                await fetch(`http://localhost:3000/api/connection/${this.connectionId}`, {
                    method: 'DELETE'
                });
            } catch (error) {
                console.error('Error closing connection:', error);
            }
        }

        this.connectionId = null;
        this.currentTable = null;
        this.currentSchema = null;
        this.currentPage = 1;
        this.searchTerm = '';

        // Reset form
        document.getElementById('connection-form').reset();
        document.getElementById('connection-status').innerHTML = '';

        this.showConnectionForm();
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    new DatabaseManager();
});