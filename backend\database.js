const sqlite3 = require('sqlite3').verbose();
const path = require('path');

class DatabaseManager {
    constructor() {
        this.dbPath = path.join(__dirname, 'connections.db');
        this.db = null;
        this.init();
    }

    init() {
        this.db = new sqlite3.Database(this.dbPath, (err) => {
            if (err) {
                console.error('Error opening database:', err);
            } else {
                console.log('Connected to SQLite database');
                this.createTables();
            }
        });
    }

    createTables() {
        const createConnectionsTable = `
            CREATE TABLE IF NOT EXISTS saved_connections (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                connection_string TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_used_at DATETIME
            )
        `;

        this.db.run(createConnectionsTable, (err) => {
            if (err) {
                console.error('Error creating connections table:', err);
            } else {
                console.log('Connections table ready');
            }
        });
    }

    // Save a new connection
    saveConnection(name, connectionString) {
        return new Promise((resolve, reject) => {
            const query = `
                INSERT INTO saved_connections (name, connection_string)
                VALUES (?, ?)
            `;
            
            this.db.run(query, [name, connectionString], function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({
                        id: this.lastID,
                        name,
                        connection_string: connectionString
                    });
                }
            });
        });
    }

    // Get all saved connections
    getAllConnections() {
        return new Promise((resolve, reject) => {
            const query = `
                SELECT id, name, connection_string, created_at, updated_at, last_used_at
                FROM saved_connections
                ORDER BY last_used_at DESC, name ASC
            `;
            
            this.db.all(query, [], (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    // Don't return the full connection string for security
                    const connections = rows.map(row => ({
                        id: row.id,
                        name: row.name,
                        connection_preview: this.maskConnectionString(row.connection_string),
                        created_at: row.created_at,
                        updated_at: row.updated_at,
                        last_used_at: row.last_used_at
                    }));
                    resolve(connections);
                }
            });
        });
    }

    // Get a specific connection by ID
    getConnection(id) {
        return new Promise((resolve, reject) => {
            const query = `
                SELECT id, name, connection_string, created_at, updated_at, last_used_at
                FROM saved_connections
                WHERE id = ?
            `;
            
            this.db.get(query, [id], (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    // Update connection name
    updateConnectionName(id, newName) {
        return new Promise((resolve, reject) => {
            const query = `
                UPDATE saved_connections 
                SET name = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            `;
            
            this.db.run(query, [newName, id], function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ changes: this.changes });
                }
            });
        });
    }

    // Update last used timestamp
    updateLastUsed(id) {
        return new Promise((resolve, reject) => {
            const query = `
                UPDATE saved_connections 
                SET last_used_at = CURRENT_TIMESTAMP
                WHERE id = ?
            `;
            
            this.db.run(query, [id], function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ changes: this.changes });
                }
            });
        });
    }

    // Delete a connection
    deleteConnection(id) {
        return new Promise((resolve, reject) => {
            const query = `DELETE FROM saved_connections WHERE id = ?`;
            
            this.db.run(query, [id], function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ changes: this.changes });
                }
            });
        });
    }

    // Mask connection string for display
    maskConnectionString(connectionString) {
        try {
            const url = new URL(connectionString);
            const host = url.hostname;
            const port = url.port;
            const database = url.pathname.substring(1);
            const username = url.username;
            
            return `${username ? username.substring(0, 3) + '***' : '***'}@${host}${port ? ':' + port : ''}/${database}`;
        } catch (error) {
            // If URL parsing fails, just show a generic mask
            return '***@***:****/****';
        }
    }

    // Close database connection
    close() {
        if (this.db) {
            this.db.close((err) => {
                if (err) {
                    console.error('Error closing database:', err);
                } else {
                    console.log('Database connection closed');
                }
            });
        }
    }
}

module.exports = new DatabaseManager();
