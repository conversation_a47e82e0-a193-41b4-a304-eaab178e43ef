const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');
const dbManager = require('./database');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());

// Store database connections
const connections = new Map();

// Get all saved connections
app.get('/api/saved-connections', async (req, res) => {
  try {
    const connections = await dbManager.getAllConnections();
    res.json(connections);
  } catch (error) {
    console.error('Error fetching saved connections:', error);
    res.status(500).json({
      error: 'Failed to fetch saved connections',
      details: error.message
    });
  }
});

// Save a new connection
app.post('/api/saved-connections', async (req, res) => {
  try {
    const { name, connectionString } = req.body;

    if (!name || !connectionString) {
      return res.status(400).json({
        error: 'Name and connection string are required'
      });
    }

    const savedConnection = await dbManager.saveConnection(name, connectionString);
    res.json({
      success: true,
      connection: {
        id: savedConnection.id,
        name: savedConnection.name,
        connection_preview: dbManager.maskConnectionString(connectionString)
      }
    });
  } catch (error) {
    console.error('Error saving connection:', error);
    if (error.code === 'SQLITE_CONSTRAINT_UNIQUE') {
      res.status(400).json({
        error: 'A connection with this name already exists'
      });
    } else {
      res.status(500).json({
        error: 'Failed to save connection',
        details: error.message
      });
    }
  }
});

// Update connection name
app.put('/api/saved-connections/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name } = req.body;

    if (!name) {
      return res.status(400).json({ error: 'Name is required' });
    }

    const result = await dbManager.updateConnectionName(id, name);

    if (result.changes === 0) {
      return res.status(404).json({ error: 'Connection not found' });
    }

    res.json({ success: true, message: 'Connection name updated' });
  } catch (error) {
    console.error('Error updating connection:', error);
    if (error.code === 'SQLITE_CONSTRAINT_UNIQUE') {
      res.status(400).json({
        error: 'A connection with this name already exists'
      });
    } else {
      res.status(500).json({
        error: 'Failed to update connection',
        details: error.message
      });
    }
  }
});

// Delete a saved connection
app.delete('/api/saved-connections/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await dbManager.deleteConnection(id);

    if (result.changes === 0) {
      return res.status(404).json({ error: 'Connection not found' });
    }

    res.json({ success: true, message: 'Connection deleted' });
  } catch (error) {
    console.error('Error deleting connection:', error);
    res.status(500).json({
      error: 'Failed to delete connection',
      details: error.message
    });
  }
});

// Connect to a saved connection
app.post('/api/connect-saved/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const savedConnection = await dbManager.getConnection(id);

    if (!savedConnection) {
      return res.status(404).json({ error: 'Connection not found' });
    }

    // Test the connection
    const testPool = new Pool({
      connectionString: savedConnection.connection_string,
      ssl: savedConnection.connection_string.includes('ssl=true') ||
           savedConnection.connection_string.includes('sslmode=require') ?
           { rejectUnauthorized: false } : false
    });

    const client = await testPool.connect();
    await client.query('SELECT 1');
    client.release();

    // Store the connection for future use
    const connectionId = Date.now().toString();
    connections.set(connectionId, testPool);

    // Update last used timestamp
    await dbManager.updateLastUsed(id);

    res.json({
      success: true,
      connectionId,
      connectionName: savedConnection.name,
      message: 'Connection successful'
    });
  } catch (error) {
    console.error('Connection test failed:', error);
    res.status(500).json({
      error: 'Connection failed',
      details: error.message
    });
  }
});

// Test database connection (and optionally save it)
app.post('/api/test-connection', async (req, res) => {
  try {
    const { connectionString, name, saveConnection } = req.body;

    if (!connectionString) {
      return res.status(400).json({ error: 'Connection string is required' });
    }

    // Create a temporary pool to test connection
    const testPool = new Pool({
      connectionString: connectionString,
      ssl: connectionString.includes('ssl=true') || connectionString.includes('sslmode=require') ? { rejectUnauthorized: false } : false
    });

    // Test the connection
    const client = await testPool.connect();
    await client.query('SELECT 1');
    client.release();

    // Store the connection for future use
    const connectionId = Date.now().toString();
    connections.set(connectionId, testPool);

    let savedConnectionInfo = null;

    // Save connection if requested and name provided
    if (saveConnection && name) {
      try {
        const savedConnection = await dbManager.saveConnection(name, connectionString);
        savedConnectionInfo = {
          id: savedConnection.id,
          name: savedConnection.name,
          saved: true
        };
      } catch (saveError) {
        console.error('Error saving connection:', saveError);
        // Don't fail the connection test if saving fails
      }
    }

    res.json({
      success: true,
      connectionId,
      connectionName: name || 'Unnamed Connection',
      savedConnection: savedConnectionInfo,
      message: 'Connection successful'
    });
  } catch (error) {
    console.error('Connection test failed:', error);
    res.status(500).json({
      error: 'Connection failed',
      details: error.message
    });
  }
});

// Get all tables
app.get('/api/tables/:connectionId', async (req, res) => {
  try {
    const { connectionId } = req.params;
    const pool = connections.get(connectionId);

    if (!pool) {
      return res.status(404).json({ error: 'Connection not found' });
    }

    const query = `
      SELECT
        table_name,
        table_schema
      FROM information_schema.tables
      WHERE table_schema NOT IN ('information_schema', 'pg_catalog', 'crdb_internal', 'pg_extension')
      ORDER BY table_schema, table_name
    `;

    const result = await pool.query(query);
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching tables:', error);
    res.status(500).json({
      error: 'Failed to fetch tables',
      details: error.message
    });
  }
});

// Get table structure
app.get('/api/table-structure/:connectionId/:schema/:tableName', async (req, res) => {
  try {
    const { connectionId, schema, tableName } = req.params;
    const pool = connections.get(connectionId);

    if (!pool) {
      return res.status(404).json({ error: 'Connection not found' });
    }

    const query = `
      SELECT
        column_name,
        data_type,
        is_nullable,
        column_default,
        character_maximum_length,
        numeric_precision,
        numeric_scale
      FROM information_schema.columns
      WHERE table_schema = $1 AND table_name = $2
      ORDER BY ordinal_position
    `;

    const result = await pool.query(query, [schema, tableName]);
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching table structure:', error);
    res.status(500).json({
      error: 'Failed to fetch table structure',
      details: error.message
    });
  }
});

// Get table data with pagination
app.get('/api/table-data/:connectionId/:schema/:tableName', async (req, res) => {
  try {
    const { connectionId, schema, tableName } = req.params;
    const { page = 1, limit = 50, search = '' } = req.query;
    const pool = connections.get(connectionId);

    if (!pool) {
      return res.status(404).json({ error: 'Connection not found' });
    }

    const offset = (page - 1) * limit;

    // Build search condition if search term provided
    let searchCondition = '';
    let queryParams = [limit, offset];

    if (search) {
      // Get column names for search
      const columnsQuery = `
        SELECT column_name
        FROM information_schema.columns
        WHERE table_schema = $1 AND table_name = $2
      `;
      const columnsResult = await pool.query(columnsQuery, [schema, tableName]);
      const columns = columnsResult.rows.map(row => row.column_name);

      // Create search condition for text columns
      const searchConditions = columns.map((col, index) =>
        `CAST("${col}" AS TEXT) ILIKE $${index + 3}`
      ).join(' OR ');

      if (searchConditions) {
        searchCondition = `WHERE (${searchConditions})`;
        queryParams = [...queryParams, ...columns.map(() => `%${search}%`)];
      }
    }

    // Get total count
    const countQuery = `SELECT COUNT(*) FROM "${schema}"."${tableName}" ${searchCondition}`;
    const countParams = search ? queryParams.slice(2) : [];
    const countResult = await pool.query(countQuery, countParams);
    const totalCount = parseInt(countResult.rows[0].count);

    // Get data
    const dataQuery = `
      SELECT * FROM "${schema}"."${tableName}"
      ${searchCondition}
      ORDER BY 1
      LIMIT $1 OFFSET $2
    `;

    const dataResult = await pool.query(dataQuery, queryParams);

    res.json({
      data: dataResult.rows,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalCount,
        totalPages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching table data:', error);
    res.status(500).json({
      error: 'Failed to fetch table data',
      details: error.message
    });
  }
});

// Insert new record
app.post('/api/table-data/:connectionId/:schema/:tableName', async (req, res) => {
  try {
    const { connectionId, schema, tableName } = req.params;
    const { data } = req.body;
    const pool = connections.get(connectionId);

    if (!pool) {
      return res.status(404).json({ error: 'Connection not found' });
    }

    const columns = Object.keys(data);
    const values = Object.values(data);
    const placeholders = values.map((_, index) => `$${index + 1}`).join(', ');

    const query = `
      INSERT INTO "${schema}"."${tableName}" (${columns.map(col => `"${col}"`).join(', ')})
      VALUES (${placeholders})
      RETURNING *
    `;

    const result = await pool.query(query, values);
    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error inserting record:', error);
    res.status(500).json({
      error: 'Failed to insert record',
      details: error.message
    });
  }
});

// Update record
app.put('/api/table-data/:connectionId/:schema/:tableName/:id', async (req, res) => {
  try {
    const { connectionId, schema, tableName, id } = req.params;
    const { data, primaryKey } = req.body;
    const pool = connections.get(connectionId);

    if (!pool) {
      return res.status(404).json({ error: 'Connection not found' });
    }

    const columns = Object.keys(data);
    const values = Object.values(data);
    const setClause = columns.map((col, index) => `"${col}" = $${index + 1}`).join(', ');

    const query = `
      UPDATE "${schema}"."${tableName}"
      SET ${setClause}
      WHERE "${primaryKey || 'id'}" = $${values.length + 1}
      RETURNING *
    `;

    const result = await pool.query(query, [...values, id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Record not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error updating record:', error);
    res.status(500).json({
      error: 'Failed to update record',
      details: error.message
    });
  }
});

// Delete record
app.delete('/api/table-data/:connectionId/:schema/:tableName/:id', async (req, res) => {
  try {
    const { connectionId, schema, tableName, id } = req.params;
    const { primaryKey } = req.query;
    const pool = connections.get(connectionId);

    if (!pool) {
      return res.status(404).json({ error: 'Connection not found' });
    }

    const query = `
      DELETE FROM "${schema}"."${tableName}"
      WHERE "${primaryKey || 'id'}" = $1
      RETURNING *
    `;

    const result = await pool.query(query, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Record not found' });
    }

    res.json({ success: true, deletedRecord: result.rows[0] });
  } catch (error) {
    console.error('Error deleting record:', error);
    res.status(500).json({
      error: 'Failed to delete record',
      details: error.message
    });
  }
});

// Get primary key for a table
app.get('/api/primary-key/:connectionId/:schema/:tableName', async (req, res) => {
  try {
    const { connectionId, schema, tableName } = req.params;
    const pool = connections.get(connectionId);

    if (!pool) {
      return res.status(404).json({ error: 'Connection not found' });
    }

    const query = `
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      WHERE i.indrelid = (
        SELECT oid FROM pg_class
        WHERE relname = $1 AND relnamespace = (
          SELECT oid FROM pg_namespace WHERE nspname = $2
        )
      )
      AND i.indisprimary
    `;

    const result = await pool.query(query, [tableName, schema]);
    const primaryKey = result.rows.length > 0 ? result.rows[0].attname : null;

    res.json({ primaryKey });
  } catch (error) {
    console.error('Error fetching primary key:', error);
    res.status(500).json({
      error: 'Failed to fetch primary key',
      details: error.message
    });
  }
});

// Close connection
app.delete('/api/connection/:connectionId', async (req, res) => {
  try {
    const { connectionId } = req.params;
    const pool = connections.get(connectionId);

    if (!pool) {
      return res.status(404).json({ error: 'Connection not found' });
    }

    await pool.end();
    connections.delete(connectionId);

    res.json({ success: true, message: 'Connection closed' });
  } catch (error) {
    console.error('Error closing connection:', error);
    res.status(500).json({
      error: 'Failed to close connection',
      details: error.message
    });
  }
});

// Error handling
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Start server
const server = app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Server started at ${new Date().toISOString()}`);
});

// Keep the process alive
server.on('error', (error) => {
  console.error('Server error:', error);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});