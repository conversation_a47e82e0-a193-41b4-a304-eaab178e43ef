const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());

// Store database connections
const connections = new Map();

// Test database connection
app.post('/api/test-connection', async (req, res) => {
  try {
    const { connectionString } = req.body;

    if (!connectionString) {
      return res.status(400).json({ error: 'Connection string is required' });
    }

    // Create a temporary pool to test connection
    const testPool = new Pool({
      connectionString: connectionString,
      ssl: connectionString.includes('ssl=true') || connectionString.includes('sslmode=require') ? { rejectUnauthorized: false } : false
    });

    // Test the connection
    const client = await testPool.connect();
    await client.query('SELECT 1');
    client.release();

    // Store the connection for future use
    const connectionId = Date.now().toString();
    connections.set(connectionId, testPool);

    res.json({
      success: true,
      connectionId,
      message: 'Connection successful'
    });
  } catch (error) {
    console.error('Connection test failed:', error);
    res.status(500).json({
      error: 'Connection failed',
      details: error.message
    });
  }
});

// Get all tables
app.get('/api/tables/:connectionId', async (req, res) => {
  try {
    const { connectionId } = req.params;
    const pool = connections.get(connectionId);

    if (!pool) {
      return res.status(404).json({ error: 'Connection not found' });
    }

    const query = `
      SELECT
        table_name,
        table_schema
      FROM information_schema.tables
      WHERE table_schema NOT IN ('information_schema', 'pg_catalog', 'crdb_internal', 'pg_extension')
      ORDER BY table_schema, table_name
    `;

    const result = await pool.query(query);
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching tables:', error);
    res.status(500).json({
      error: 'Failed to fetch tables',
      details: error.message
    });
  }
});

// Get table structure
app.get('/api/table-structure/:connectionId/:schema/:tableName', async (req, res) => {
  try {
    const { connectionId, schema, tableName } = req.params;
    const pool = connections.get(connectionId);

    if (!pool) {
      return res.status(404).json({ error: 'Connection not found' });
    }

    const query = `
      SELECT
        column_name,
        data_type,
        is_nullable,
        column_default,
        character_maximum_length,
        numeric_precision,
        numeric_scale
      FROM information_schema.columns
      WHERE table_schema = $1 AND table_name = $2
      ORDER BY ordinal_position
    `;

    const result = await pool.query(query, [schema, tableName]);
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching table structure:', error);
    res.status(500).json({
      error: 'Failed to fetch table structure',
      details: error.message
    });
  }
});

// Get table data with pagination
app.get('/api/table-data/:connectionId/:schema/:tableName', async (req, res) => {
  try {
    const { connectionId, schema, tableName } = req.params;
    const { page = 1, limit = 50, search = '' } = req.query;
    const pool = connections.get(connectionId);

    if (!pool) {
      return res.status(404).json({ error: 'Connection not found' });
    }

    const offset = (page - 1) * limit;

    // Build search condition if search term provided
    let searchCondition = '';
    let queryParams = [limit, offset];

    if (search) {
      // Get column names for search
      const columnsQuery = `
        SELECT column_name
        FROM information_schema.columns
        WHERE table_schema = $1 AND table_name = $2
      `;
      const columnsResult = await pool.query(columnsQuery, [schema, tableName]);
      const columns = columnsResult.rows.map(row => row.column_name);

      // Create search condition for text columns
      const searchConditions = columns.map((col, index) =>
        `CAST("${col}" AS TEXT) ILIKE $${index + 3}`
      ).join(' OR ');

      if (searchConditions) {
        searchCondition = `WHERE (${searchConditions})`;
        queryParams = [...queryParams, ...columns.map(() => `%${search}%`)];
      }
    }

    // Get total count
    const countQuery = `SELECT COUNT(*) FROM "${schema}"."${tableName}" ${searchCondition}`;
    const countParams = search ? queryParams.slice(2) : [];
    const countResult = await pool.query(countQuery, countParams);
    const totalCount = parseInt(countResult.rows[0].count);

    // Get data
    const dataQuery = `
      SELECT * FROM "${schema}"."${tableName}"
      ${searchCondition}
      ORDER BY 1
      LIMIT $1 OFFSET $2
    `;

    const dataResult = await pool.query(dataQuery, queryParams);

    res.json({
      data: dataResult.rows,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalCount,
        totalPages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching table data:', error);
    res.status(500).json({
      error: 'Failed to fetch table data',
      details: error.message
    });
  }
});

// Insert new record
app.post('/api/table-data/:connectionId/:schema/:tableName', async (req, res) => {
  try {
    const { connectionId, schema, tableName } = req.params;
    const { data } = req.body;
    const pool = connections.get(connectionId);

    if (!pool) {
      return res.status(404).json({ error: 'Connection not found' });
    }

    const columns = Object.keys(data);
    const values = Object.values(data);
    const placeholders = values.map((_, index) => `$${index + 1}`).join(', ');

    const query = `
      INSERT INTO "${schema}"."${tableName}" (${columns.map(col => `"${col}"`).join(', ')})
      VALUES (${placeholders})
      RETURNING *
    `;

    const result = await pool.query(query, values);
    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error inserting record:', error);
    res.status(500).json({
      error: 'Failed to insert record',
      details: error.message
    });
  }
});

// Update record
app.put('/api/table-data/:connectionId/:schema/:tableName/:id', async (req, res) => {
  try {
    const { connectionId, schema, tableName, id } = req.params;
    const { data, primaryKey } = req.body;
    const pool = connections.get(connectionId);

    if (!pool) {
      return res.status(404).json({ error: 'Connection not found' });
    }

    const columns = Object.keys(data);
    const values = Object.values(data);
    const setClause = columns.map((col, index) => `"${col}" = $${index + 1}`).join(', ');

    const query = `
      UPDATE "${schema}"."${tableName}"
      SET ${setClause}
      WHERE "${primaryKey || 'id'}" = $${values.length + 1}
      RETURNING *
    `;

    const result = await pool.query(query, [...values, id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Record not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error updating record:', error);
    res.status(500).json({
      error: 'Failed to update record',
      details: error.message
    });
  }
});

// Delete record
app.delete('/api/table-data/:connectionId/:schema/:tableName/:id', async (req, res) => {
  try {
    const { connectionId, schema, tableName, id } = req.params;
    const { primaryKey } = req.query;
    const pool = connections.get(connectionId);

    if (!pool) {
      return res.status(404).json({ error: 'Connection not found' });
    }

    const query = `
      DELETE FROM "${schema}"."${tableName}"
      WHERE "${primaryKey || 'id'}" = $1
      RETURNING *
    `;

    const result = await pool.query(query, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Record not found' });
    }

    res.json({ success: true, deletedRecord: result.rows[0] });
  } catch (error) {
    console.error('Error deleting record:', error);
    res.status(500).json({
      error: 'Failed to delete record',
      details: error.message
    });
  }
});

// Get primary key for a table
app.get('/api/primary-key/:connectionId/:schema/:tableName', async (req, res) => {
  try {
    const { connectionId, schema, tableName } = req.params;
    const pool = connections.get(connectionId);

    if (!pool) {
      return res.status(404).json({ error: 'Connection not found' });
    }

    const query = `
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      WHERE i.indrelid = (
        SELECT oid FROM pg_class
        WHERE relname = $1 AND relnamespace = (
          SELECT oid FROM pg_namespace WHERE nspname = $2
        )
      )
      AND i.indisprimary
    `;

    const result = await pool.query(query, [tableName, schema]);
    const primaryKey = result.rows.length > 0 ? result.rows[0].attname : null;

    res.json({ primaryKey });
  } catch (error) {
    console.error('Error fetching primary key:', error);
    res.status(500).json({
      error: 'Failed to fetch primary key',
      details: error.message
    });
  }
});

// Close connection
app.delete('/api/connection/:connectionId', async (req, res) => {
  try {
    const { connectionId } = req.params;
    const pool = connections.get(connectionId);

    if (!pool) {
      return res.status(404).json({ error: 'Connection not found' });
    }

    await pool.end();
    connections.delete(connectionId);

    res.json({ success: true, message: 'Connection closed' });
  } catch (error) {
    console.error('Error closing connection:', error);
    res.status(500).json({
      error: 'Failed to close connection',
      details: error.message
    });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});