const express = require('express');
const { Pool } = require('pg');
const cors = require('cors');

const app = express();
const port = 3000;

app.use(cors());
app.use(express.json());

let pool;

app.post('/connect', (req, res) => {
    const { connectionString } = req.body;
    pool = new Pool({
        connectionString,
    });
    pool.connect((err, client, release) => {
        if (err) {
            console.error('Error acquiring client', err.stack)
            return res.status(500).send('Error connecting to database');
        }
        client.query('SELECT NOW()', (err, result) => {
            release();
            if (err) {
                console.error('Error executing query', err.stack)
                return res.status(500).send('Error executing query');
            }
            console.log('Connected to database');
            res.sendStatus(200);
        });
    });
});

app.get('/tables', async (req, res) => {
    try {
        const result = await pool.query("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name");
        res.json(result.rows.map(row => row.table_name));
    } catch (error) {
        console.error(error);
        res.status(500).send('Error fetching tables');
    }
});

app.get('/tables/:tableName/pk', async (req, res) => {
    const { tableName } = req.params;
    try {
        const result = await pool.query(`
            SELECT kcu.column_name
            FROM information_schema.table_constraints AS tc
            JOIN information_schema.key_column_usage AS kcu
                ON tc.constraint_name = kcu.constraint_name
                AND tc.table_schema = kcu.table_schema
            WHERE tc.constraint_type = 'PRIMARY KEY'
            AND tc.table_name = $1
            AND tc.table_schema = 'public'
        `, [tableName]);

        if (result.rows.length > 0) {
            res.json({ primaryKey: result.rows[0].column_name });
        } else {
            res.status(404).send('Primary key not found for this table.');
        }
    } catch (error) {
        console.error(error);
        res.status(500).send('Error fetching primary key');
    }
});

app.get('/tables/:tableName', async (req, res) => {
    const { tableName } = req.params;
    try {
        const result = await pool.query(`SELECT * FROM "${tableName}"`);
        res.json(result.rows);
    } catch (error) {
        console.error(error);
        res.status(500).send(`Error fetching data from ${tableName}`);
    }
});

app.put('/tables/:tableName/:pkColumn/:pkValue', async (req, res) => {
    const { tableName, pkColumn, pkValue } = req.params;
    const updates = req.body;
    delete updates[pkColumn]; 

    const setClause = Object.keys(updates).map((key, i) => `"${key}" = $${i + 1}`).join(', ');
    const values = Object.values(updates);

    try {
        const query = {
            text: `UPDATE "${tableName}" SET ${setClause} WHERE "${pkColumn}" = $${values.length + 1}`,
            values: [...values, pkValue]
        };
        await pool.query(query);
        res.sendStatus(200);
    } catch (error) {
        console.error(error);
        res.status(500).send(`Error updating row in ${tableName}`);
    }
});

app.delete('/tables/:tableName/:pkColumn/:pkValue', async (req, res) => {
    const { tableName, pkColumn, pkValue } = req.params;
    try {
        await pool.query(`DELETE FROM "${tableName}" WHERE "${pkColumn}" = $1`, [pkValue]);
        res.sendStatus(200);
    } catch (error) {
        console.error(error);
        res.status(500).send(`Error deleting row from ${tableName}`);
    }
});

app.listen(port, () => {
    console.log(`Server listening at http://localhost:${port}`);
});
