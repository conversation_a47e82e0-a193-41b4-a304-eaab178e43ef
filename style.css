body {
    font-family: sans-serif;
    margin: 0;
    background-color: #f0f2f5;
}

#app {
    display: flex;
    height: 100vh;
}

#login-container {
    margin: auto;
    padding: 2rem;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    text-align: center;
}

#main-container {
    display: flex;
    width: 100%;
}

#sidebar {
    width: 250px;
    background-color: #fff;
    border-right: 1px solid #ddd;
    padding: 1rem;
}

#content {
    flex-grow: 1;
    padding: 1rem;
}

#table-list {
    list-style: none;
    padding: 0;
}

#table-list li {
    padding: 0.5rem;
    cursor: pointer;
}

#table-list li:hover {
    background-color: #eee;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    border: 1px solid #ddd;
    padding: 0.5rem;
    text-align: left;
}

th {
    background-color: #f2f2f2;
}
