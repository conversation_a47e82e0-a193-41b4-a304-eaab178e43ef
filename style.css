/* CSS Variables inspired by reference UI */
:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --success: oklch(0.646 0.222 41.116);
  --warning: oklch(0.828 0.189 84.429);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
}

/* Base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: var(--background);
  color: var(--foreground);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Connection Form Styles */
.connection-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 2rem;
  background: linear-gradient(135deg, var(--muted) 0%, var(--background) 100%);
}

.connection-form {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 2.5rem;
  width: 100%;
  max-width: 500px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.form-header {
  text-align: center;
  margin-bottom: 2rem;
}

.form-header i {
  font-size: 3rem;
  color: var(--primary);
  margin-bottom: 1rem;
}

.form-header h1 {
  font-size: 1.875rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: var(--foreground);
}

.form-header p {
  color: var(--muted-foreground);
  font-size: 0.875rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: var(--foreground);
}

.form-group input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border);
  border-radius: calc(var(--radius) - 2px);
  background: var(--background);
  color: var(--foreground);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.form-group input:focus {
  outline: none;
  border-color: var(--ring);
  box-shadow: 0 0 0 2px var(--ring);
}

.form-help {
  display: block;
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: var(--muted-foreground);
}

/* Saved Connections Section */
.saved-connections-section {
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid var(--border);
}

.saved-connections-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--foreground);
}

.saved-connections-list {
  margin-bottom: 1rem;
  max-height: 200px;
  overflow-y: auto;
}

.connection-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  border: 1px solid var(--border);
  border-radius: calc(var(--radius) - 2px);
  margin-bottom: 0.5rem;
  background: var(--card);
  transition: all 0.2s ease;
  cursor: pointer;
}

.connection-item:hover {
  background: var(--accent);
  border-color: var(--ring);
}

.connection-item.recent {
  border-color: var(--primary);
  background: oklch(from var(--primary) l c h / 0.05);
}

.connection-details {
  flex: 1;
  min-width: 0;
}

.connection-name {
  font-weight: 500;
  font-size: 0.875rem;
  color: var(--foreground);
  margin-bottom: 0.25rem;
}

.connection-preview {
  font-size: 0.75rem;
  color: var(--muted-foreground);
  font-family: 'Courier New', monospace;
}

.connection-meta {
  font-size: 0.75rem;
  color: var(--muted-foreground);
  margin-top: 0.25rem;
}

.connection-actions {
  display: flex;
  gap: 0.5rem;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.connection-item:hover .connection-actions {
  opacity: 1;
}

.connection-divider {
  text-align: center;
  margin: 1.5rem 0;
  position: relative;
}

.connection-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--border);
}

.connection-divider span {
  background: var(--background);
  padding: 0 1rem;
  color: var(--muted-foreground);
  font-size: 0.75rem;
  font-weight: 500;
}

/* Checkbox Styles */
.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.875rem;
  color: var(--foreground);
}

.checkbox-label input[type="checkbox"] {
  width: 1rem;
  height: 1rem;
  margin: 0;
}

.checkmark {
  font-size: 0.875rem;
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: 1px solid transparent;
  border-radius: calc(var(--radius) - 2px);
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--primary);
  color: var(--primary-foreground);
  border-color: var(--primary);
}

.btn-primary:hover:not(:disabled) {
  background: oklch(from var(--primary) calc(l - 0.05) c h);
}

.btn-outline {
  background: transparent;
  color: var(--foreground);
  border-color: var(--border);
}

.btn-outline:hover:not(:disabled) {
  background: var(--accent);
  color: var(--accent-foreground);
}

.btn-danger {
  background: var(--destructive);
  color: var(--primary-foreground);
  border-color: var(--destructive);
}

.btn-danger:hover:not(:disabled) {
  background: oklch(from var(--destructive) calc(l - 0.05) c h);
}

.btn-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border: none;
  border-radius: calc(var(--radius) - 4px);
  background: transparent;
  color: var(--muted-foreground);
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-icon:hover {
  background: var(--accent);
  color: var(--accent-foreground);
}

.btn-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 2rem;
  height: 2rem;
  border: none;
  border-radius: calc(var(--radius) - 4px);
  background: transparent;
  color: var(--muted-foreground);
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-close:hover {
  background: var(--accent);
  color: var(--accent-foreground);
}

/* Status Message */
.status-message {
  margin-top: 1rem;
  padding: 0.75rem;
  border-radius: calc(var(--radius) - 2px);
  font-size: 0.875rem;
  text-align: center;
}

.status-message.success {
  background: oklch(from var(--success) l c h / 0.1);
  color: var(--success);
  border: 1px solid oklch(from var(--success) l c h / 0.2);
}

.status-message.error {
  background: oklch(from var(--destructive) l c h / 0.1);
  color: var(--destructive);
  border: 1px solid oklch(from var(--destructive) l c h / 0.2);
}

/* Main Application Layout */
.main-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.app-header {
  background: var(--card);
  border-bottom: 1px solid var(--border);
  padding: 1rem 1.5rem;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1400px;
  margin: 0 auto;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-left i {
  font-size: 1.5rem;
  color: var(--primary);
}

.header-left h1 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--foreground);
}

.connection-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.connection-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  background: oklch(from var(--success) l c h / 0.1);
  color: var(--success);
  border-radius: calc(var(--radius) - 4px);
  font-size: 0.75rem;
  font-weight: 500;
}

.connection-indicator i {
  font-size: 0.5rem;
}

.connection-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--foreground);
  padding: 0 0.75rem;
}

.header-right {
  display: flex;
  gap: 0.75rem;
}

.app-body {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* Sidebar Styles */
.sidebar {
  width: 300px;
  background: var(--card);
  border-right: 1px solid var(--border);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 1.5rem 1rem;
  border-bottom: 1px solid var(--border);
}

.sidebar-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--foreground);
}

.search-container {
  position: relative;
  margin: 1rem 1.5rem;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid var(--border);
  border-radius: calc(var(--radius) - 2px);
  background: var(--background);
  color: var(--foreground);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--ring);
  box-shadow: 0 0 0 2px var(--ring);
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--muted-foreground);
  font-size: 0.875rem;
}

.tables-list {
  flex: 1;
  overflow-y: auto;
  padding: 0 1.5rem 1.5rem;
}

.table-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: calc(var(--radius) - 2px);
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 0.25rem;
}

.table-item:hover {
  background: var(--accent);
  color: var(--accent-foreground);
}

.table-item.active {
  background: var(--primary);
  color: var(--primary-foreground);
}

.table-item i {
  font-size: 1rem;
  color: currentColor;
  opacity: 0.7;
}

.table-info {
  flex: 1;
  min-width: 0;
}

.table-name {
  font-weight: 500;
  font-size: 0.875rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.table-schema {
  font-size: 0.75rem;
  opacity: 0.7;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Loading Styles */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 2rem;
  color: var(--muted-foreground);
  font-size: 0.875rem;
}

.loading i {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Main Content Styles */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: var(--background);
}

.welcome-screen {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  padding: 2rem;
}

.welcome-content {
  text-align: center;
  color: var(--muted-foreground);
}

.welcome-content i {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.welcome-content h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--foreground);
}

.welcome-content p {
  font-size: 0.875rem;
}

/* Table View Styles */
.table-view {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.table-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border);
  background: var(--card);
}

.table-info h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--foreground);
  margin-bottom: 0.25rem;
}

.record-count {
  font-size: 0.875rem;
  color: var(--muted-foreground);
}

.table-actions {
  display: flex;
  gap: 0.75rem;
}

.table-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border);
  background: var(--card);
}

.pagination-info {
  font-size: 0.875rem;
  color: var(--muted-foreground);
}

/* Table Container */
.table-container {
  flex: 1;
  overflow: auto;
  position: relative;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--card);
}

.data-table th {
  background: var(--muted);
  color: var(--muted-foreground);
  font-weight: 500;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid var(--border);
  position: sticky;
  top: 0;
  z-index: 10;
}

.data-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border);
  color: var(--foreground);
  font-size: 0.875rem;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.data-table tr:hover {
  background: var(--accent);
}

.data-table .actions-cell {
  width: 100px;
  text-align: right;
}

.record-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.record-actions .btn-icon {
  width: 1.75rem;
  height: 1.75rem;
  font-size: 0.75rem;
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: var(--muted-foreground);
}

.no-data i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-data p {
  font-size: 0.875rem;
}

/* Pagination */
.pagination-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border);
  background: var(--card);
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.page-numbers {
  display: flex;
  gap: 0.25rem;
  margin: 0 0.5rem;
}

.page-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border: 1px solid var(--border);
  border-radius: calc(var(--radius) - 4px);
  background: transparent;
  color: var(--foreground);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.page-number:hover {
  background: var(--accent);
  color: var(--accent-foreground);
}

.page-number.active {
  background: var(--primary);
  color: var(--primary-foreground);
  border-color: var(--primary);
}

#records-info {
  font-size: 0.875rem;
  color: var(--muted-foreground);
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  position: relative;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
}

.modal-small {
  max-width: 400px;
}

.modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border);
  position: relative;
}

.modal-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--foreground);
  margin: 0;
  padding-right: 3rem;
}

.modal-body {
  padding: 1.5rem;
  overflow-y: auto;
  max-height: calc(90vh - 200px);
}

.modal-footer {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border);
  margin-top: 1.5rem;
}

.form-fields {
  display: grid;
  gap: 1rem;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-field label {
  font-weight: 500;
  color: var(--foreground);
  font-size: 0.875rem;
}

.form-field input,
.form-field textarea,
.form-field select {
  padding: 0.75rem;
  border: 1px solid var(--border);
  border-radius: calc(var(--radius) - 2px);
  background: var(--background);
  color: var(--foreground);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.form-field input:focus,
.form-field textarea:focus,
.form-field select:focus {
  outline: none;
  border-color: var(--ring);
  box-shadow: 0 0 0 2px var(--ring);
}

.form-field textarea {
  resize: vertical;
  min-height: 80px;
}

.confirm-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.confirm-content i {
  font-size: 2rem;
  color: var(--warning);
}

.confirm-content p {
  color: var(--foreground);
  font-size: 0.875rem;
}

/* Connection Switcher Modal */
.switcher-connections-list {
  max-height: 400px;
  overflow-y: auto;
  margin-bottom: 1.5rem;
}

.switcher-connection-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border: 1px solid var(--border);
  border-radius: calc(var(--radius) - 2px);
  margin-bottom: 0.75rem;
  background: var(--card);
  transition: all 0.2s ease;
  cursor: pointer;
}

.switcher-connection-item:hover {
  background: var(--accent);
  border-color: var(--ring);
}

.switcher-connection-item.current {
  border-color: var(--primary);
  background: oklch(from var(--primary) l c h / 0.1);
}

.switcher-connection-details {
  flex: 1;
  min-width: 0;
}

.switcher-connection-name {
  font-weight: 600;
  font-size: 1rem;
  color: var(--foreground);
  margin-bottom: 0.25rem;
}

.switcher-connection-preview {
  font-size: 0.75rem;
  color: var(--muted-foreground);
  font-family: 'Courier New', monospace;
  margin-bottom: 0.25rem;
}

.switcher-connection-meta {
  font-size: 0.75rem;
  color: var(--muted-foreground);
}

.switcher-connection-actions {
  display: flex;
  gap: 0.5rem;
}

.current-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: var(--primary);
  color: var(--primary-foreground);
  border-radius: calc(var(--radius) - 4px);
  font-size: 0.75rem;
  font-weight: 500;
}

.current-badge i {
  font-size: 0.625rem;
}

/* Toast Notifications */
.toast-container {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 2000;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.toast {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 1rem 1.5rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  min-width: 300px;
  max-width: 400px;
  animation: slideIn 0.3s ease;
}

.toast.success {
  border-left: 4px solid var(--success);
}

.toast.error {
  border-left: 4px solid var(--destructive);
}

.toast.warning {
  border-left: 4px solid var(--warning);
}

.toast i {
  font-size: 1.25rem;
}

.toast.success i {
  color: var(--success);
}

.toast.error i {
  color: var(--destructive);
}

.toast.warning i {
  color: var(--warning);
}

.toast-content {
  flex: 1;
}

.toast-title {
  font-weight: 500;
  font-size: 0.875rem;
  color: var(--foreground);
  margin-bottom: 0.25rem;
}

.toast-message {
  font-size: 0.75rem;
  color: var(--muted-foreground);
}

.toast-close {
  background: none;
  border: none;
  color: var(--muted-foreground);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: calc(var(--radius) - 4px);
  transition: all 0.2s ease;
}

.toast-close:hover {
  background: var(--accent);
  color: var(--accent-foreground);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar {
    width: 250px;
  }

  .table-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .table-actions {
    justify-content: stretch;
  }

  .table-controls {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .pagination-container {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .pagination-controls {
    justify-content: center;
  }

  .data-table td {
    max-width: 150px;
  }

  .modal-content {
    margin: 1rem;
    max-width: calc(100% - 2rem);
  }

  .toast {
    min-width: 250px;
    max-width: calc(100vw - 2rem);
  }
}

@media (max-width: 640px) {
  .app-body {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: 200px;
    border-right: none;
    border-bottom: 1px solid var(--border);
  }

  .connection-form {
    padding: 1.5rem;
  }

  .form-header h1 {
    font-size: 1.5rem;
  }
}
