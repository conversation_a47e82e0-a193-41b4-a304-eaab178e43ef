# PostgreSQL Database Manager

A modern web-based database management system for PostgreSQL and CockroachDB databases. This application allows you to connect to your database, view tables, and perform CRUD operations on your data through an intuitive web interface.

## Features

- **Database Connection**: Connect to PostgreSQL or CockroachDB using connection strings
- **Table Browser**: View all tables in your database with schema information
- **Data Viewer**: Browse table data with pagination and search functionality
- **CRUD Operations**: Create, read, update, and delete records directly from the web interface
- **Responsive Design**: Modern UI that works on desktop and mobile devices
- **Real-time Updates**: Instant feedback with toast notifications
- **Type-aware Forms**: Automatically generates appropriate form inputs based on column data types

## Prerequisites

- Node.js (v14 or higher)
- A PostgreSQL or CockroachDB database

## Installation

1. Clone or download this repository
2. Navigate to the backend directory:
   ```bash
   cd backend
   ```
3. Install dependencies:
   ```bash
   npm install
   ```

## Usage

### Starting the Backend Server

1. Navigate to the backend directory:
   ```bash
   cd backend
   ```
2. Start the server:
   ```bash
   node index.js
   ```
3. The server will start on port 3000

### Using the Web Interface

1. Open `index.html` in your web browser
2. Enter your database connection string in the format:
   ```
   postgresql://username:password@host:port/database?sslmode=require
   ```
3. Click "Connect to Database"
4. Once connected, you'll see:
   - A sidebar with all available tables
   - Click on any table to view its data
   - Use the search box to filter tables or data
   - Add, edit, or delete records using the action buttons

### Connection String Examples

**CockroachDB:**
```
*****************************************/defaultdb?sslmode=require
```

**Local PostgreSQL:**
```
postgresql://username:password@localhost:5432/database_name
```

**Heroku PostgreSQL:**
```
**************************************/database_name
```

## Project Structure

```
db_manager/
├── index.html          # Main HTML file
├── style.css           # CSS styling
├── script.js           # Frontend JavaScript
├── backend/
│   ├── index.js        # Express server
│   ├── package.json    # Backend dependencies
│   └── node_modules/   # Installed packages
└── reference_ui/       # UI reference (for styling inspiration)
```

## API Endpoints

The backend provides the following REST API endpoints:

- `POST /api/test-connection` - Test database connection
- `GET /api/tables/:connectionId` - Get all tables
- `GET /api/table-structure/:connectionId/:schema/:tableName` - Get table structure
- `GET /api/table-data/:connectionId/:schema/:tableName` - Get table data with pagination
- `POST /api/table-data/:connectionId/:schema/:tableName` - Insert new record
- `PUT /api/table-data/:connectionId/:schema/:tableName/:id` - Update record
- `DELETE /api/table-data/:connectionId/:schema/:tableName/:id` - Delete record
- `GET /api/primary-key/:connectionId/:schema/:tableName` - Get primary key
- `DELETE /api/connection/:connectionId` - Close connection

## Security Considerations

- This is a development tool and should not be exposed to the internet without proper authentication
- Database credentials are handled in memory only
- Always use SSL connections for production databases
- Consider implementing user authentication for production use

## Browser Compatibility

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## Troubleshooting

### Connection Issues
- Verify your connection string format
- Check if the database server is accessible
- Ensure SSL settings match your database requirements
- Check firewall settings

### CORS Issues
- The backend includes CORS headers for local development
- For production, configure CORS appropriately

### Performance
- Large tables are paginated (50 records per page by default)
- Use the search functionality to filter large datasets
- Consider adding indexes to frequently searched columns

## Contributing

This is a standalone database management tool. Feel free to modify and extend it according to your needs.

## License

This project is open source and available under the MIT License.
