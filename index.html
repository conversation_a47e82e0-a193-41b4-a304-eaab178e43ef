<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PostgreSQL Database Manager</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div id="app">
        <!-- Connection Form -->
        <div id="connection-container" class="connection-container">
            <div class="connection-form">
                <div class="form-header">
                    <i class="fas fa-database"></i>
                    <h1>PostgreSQL Database Manager</h1>
                    <p>Connect to your CockroachDB or PostgreSQL database</p>
                </div>

                <form id="connection-form">
                    <div class="form-group">
                        <label for="connection-string">Database Connection String</label>
                        <input
                            type="text"
                            id="connection-string"
                            name="connectionString"
                            placeholder="postgresql://username:password@host:port/database?sslmode=require"
                            required
                        >
                        <small class="form-help">
                            Example: *********************************/defaultdb?sslmode=require
                        </small>
                    </div>

                    <button type="submit" class="btn btn-primary" id="connect-btn">
                        <i class="fas fa-plug"></i>
                        Connect to Database
                    </button>

                    <div id="connection-status" class="status-message"></div>
                </form>
            </div>
        </div>

        <!-- Main Application -->
        <div id="main-container" class="main-container" style="display: none;">
            <!-- Header -->
            <header class="app-header">
                <div class="header-content">
                    <div class="header-left">
                        <i class="fas fa-database"></i>
                        <h1>Database Manager</h1>
                        <span class="connection-indicator">
                            <i class="fas fa-circle"></i>
                            Connected
                        </span>
                    </div>
                    <div class="header-right">
                        <button id="disconnect-btn" class="btn btn-outline">
                            <i class="fas fa-sign-out-alt"></i>
                            Disconnect
                        </button>
                    </div>
                </div>
            </header>

            <div class="app-body">
                <!-- Sidebar -->
                <aside class="sidebar">
                    <div class="sidebar-header">
                        <h3>Tables</h3>
                        <button id="refresh-tables" class="btn-icon" title="Refresh tables">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>

                    <div class="search-container">
                        <input
                            type="text"
                            id="table-search"
                            placeholder="Search tables..."
                            class="search-input"
                        >
                        <i class="fas fa-search search-icon"></i>
                    </div>

                    <div id="tables-list" class="tables-list">
                        <div class="loading">
                            <i class="fas fa-spinner fa-spin"></i>
                            Loading tables...
                        </div>
                    </div>
                </aside>

                <!-- Main Content -->
                <main class="main-content">
                    <div id="welcome-screen" class="welcome-screen">
                        <div class="welcome-content">
                            <i class="fas fa-table"></i>
                            <h2>Welcome to Database Manager</h2>
                            <p>Select a table from the sidebar to view and manage its data</p>
                        </div>
                    </div>

                    <div id="table-view" class="table-view" style="display: none;">
                        <!-- Table Header -->
                        <div class="table-header">
                            <div class="table-info">
                                <h2 id="current-table-name">Table Name</h2>
                                <span id="table-record-count" class="record-count">0 records</span>
                            </div>
                            <div class="table-actions">
                                <button id="add-record-btn" class="btn btn-primary">
                                    <i class="fas fa-plus"></i>
                                    Add Record
                                </button>
                                <button id="refresh-data-btn" class="btn btn-outline">
                                    <i class="fas fa-sync-alt"></i>
                                    Refresh
                                </button>
                            </div>
                        </div>

                        <!-- Search and Filters -->
                        <div class="table-controls">
                            <div class="search-container">
                                <input
                                    type="text"
                                    id="data-search"
                                    placeholder="Search records..."
                                    class="search-input"
                                >
                                <i class="fas fa-search search-icon"></i>
                            </div>
                            <div class="pagination-info">
                                <span id="pagination-text">Page 1 of 1</span>
                            </div>
                        </div>

                        <!-- Table Data -->
                        <div class="table-container">
                            <div id="table-loading" class="loading" style="display: none;">
                                <i class="fas fa-spinner fa-spin"></i>
                                Loading data...
                            </div>

                            <table id="data-table" class="data-table">
                                <thead id="table-head">
                                    <!-- Dynamic headers -->
                                </thead>
                                <tbody id="table-body">
                                    <!-- Dynamic data -->
                                </tbody>
                            </table>

                            <div id="no-data" class="no-data" style="display: none;">
                                <i class="fas fa-inbox"></i>
                                <p>No data found</p>
                            </div>
                        </div>

                        <!-- Pagination -->
                        <div class="pagination-container">
                            <div class="pagination-info">
                                <span id="records-info">Showing 0 to 0 of 0 records</span>
                            </div>
                            <div class="pagination-controls">
                                <button id="prev-page" class="btn btn-outline" disabled>
                                    <i class="fas fa-chevron-left"></i>
                                    Previous
                                </button>
                                <span id="page-numbers" class="page-numbers"></span>
                                <button id="next-page" class="btn btn-outline" disabled>
                                    Next
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </div>

        <!-- Modal for Add/Edit Record -->
        <div id="record-modal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="modal-title">Add New Record</h3>
                    <button id="close-modal" class="btn-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <form id="record-form" class="modal-body">
                    <div id="form-fields" class="form-fields">
                        <!-- Dynamic form fields will be inserted here -->
                    </div>

                    <div class="modal-footer">
                        <button type="button" id="cancel-btn" class="btn btn-outline">
                            Cancel
                        </button>
                        <button type="submit" id="save-btn" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            Save Record
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Confirmation Modal -->
        <div id="confirm-modal" class="modal" style="display: none;">
            <div class="modal-content modal-small">
                <div class="modal-header">
                    <h3>Confirm Action</h3>
                    <button id="close-confirm-modal" class="btn-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="modal-body">
                    <div class="confirm-content">
                        <i class="fas fa-exclamation-triangle"></i>
                        <p id="confirm-message">Are you sure you want to perform this action?</p>
                    </div>

                    <div class="modal-footer">
                        <button type="button" id="confirm-cancel" class="btn btn-outline">
                            Cancel
                        </button>
                        <button type="button" id="confirm-action" class="btn btn-danger">
                            <i class="fas fa-trash"></i>
                            Delete
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Toast Notifications -->
        <div id="toast-container" class="toast-container"></div>
    </div>

    <script src="script.js"></script>
</body>
</html>